import { ref, readonly } from 'vue';

interface User {
  id: number;
  email: string;
  organization_id: string;
  role?: 'admin' | 'agent';
}

const currentUser = ref<User | null>(null);

export const useUser = () => {
  const setUser = (user: User) => {
    currentUser.value = user;
  };

  const getUser = () => {
    return readonly(currentUser);
  };

  const getUserRole = () => {
    return currentUser.value?.role || 'agent';
  };

  const isAdmin = () => {
    return currentUser.value?.role === 'admin';
  };

  const isAgent = () => {
    return currentUser.value?.role === 'agent';
  };

  const getUserOrganizationId = () => {
    return currentUser.value?.organization_id;
  };

  const initializeUser = () => {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const user = JSON.parse(storedUser);
        currentUser.value = user;
      } catch (error) {
        console.error('Error parsing stored user:', error);
        localStorage.removeItem('user');
      }
    }
  };

  const clearUser = () => {
    currentUser.value = null;
    localStorage.removeItem('user');
  };

  return {
    setUser,
    getUser,
    getUserRole,
    isAdmin,
    isAgent,
    getUserOrganizationId,
    initializeUser,
    clearUser
  };
};
