<template>
  <div class="agent-list">
    <div class="overflow-x-auto">
      <div v-if="agents.length === 0" class="text-center py-8">
        <p class="text-gray-500 text-lg">No agents available</p>
        <div class="flex justify-center space-x-4 mt-4">
          <button
            @click="router.push('/agents/create')"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Create New Agent
          </button>
        </div>
      </div>
      <table v-else class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Agent ID</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Organization</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">AI Score</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completed Cases</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Skills</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="agent in agents" :key="agent.id">
            <td class="px-6 py-4 whitespace-nowrap">{{ agent.id }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              <router-link :to="{ name: 'agent-details', params: { id: agent.id } }" class="text-indigo-600 hover:text-indigo-900">
                {{ agent.firstName + " " + agent.lastName}}
              </router-link>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">{{ agent.organization.name }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ agent.address }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ agent.aiClassifiedScore ? agent.aiClassifiedScore.toFixed(2) : '0.00' }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ getCompletedCases(agent) }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ agent.completedSkills?.join(', ') || 'None' }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <button @click="viewAgentDetails(agent.id)" class="text-indigo-600 hover:text-indigo-900 mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z" />
                  <path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd" />
                </svg>
              </button>
              <button @click="deleteAgent(agent.id)" class="text-red-600 hover:text-red-900" title="Delete Agent">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </button>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Pagination for Agents -->
      <Pagination
        v-if="agents.length > 0"
        :current-page="paginationState.currentPage"
        :total-pages="paginationState.totalPages"
        :total-items="paginationState.totalItems"
        :page-size="paginationState.pageSize"
        @page-change="handlePageChange($event)"
        @page-size-change="handlePageSizeChange($event)"
      />
    </div>

    <!-- Auto Assign Modal -->
    <Teleport to="body">
      <div v-if="props.showAutoAssignModal" class="modal">
        <div class="modal-content">
          <h3>Auto Assign Agent</h3>
          <div class="form-group">
            <label for="case-select">Case:</label>
            <select
              id="case-select"
              v-model="selectedCaseId"
              :disabled="isCheckingCase"
              class="block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option :value="null" disabled>Select a case</option>
              <option
                v-for="caseItem in availableCases"
                :key="caseItem.caseId"
                :value="caseItem.caseId"
              >
                {{ caseItem.patient?.firstName || '' }} {{ caseItem.patient?.lastName || '' }} - {{ caseItem.prescriber?.firstName || '' }} {{ caseItem.prescriber?.lastName || '' }}
              </option>
            </select>
          </div>
          <div class="modal-actions">
            <button @click="autoAssignAgent" class="primary-btn" :disabled="isCheckingCase">
              {{ isCheckingCase ? 'Checking...' : 'Assign' }}
            </button>
            <button @click="closeAutoAssignModal" class="secondary-btn" :disabled="isCheckingCase">
              Cancel
            </button>
          </div>
        </div>
      </div>
    </Teleport>

    <!-- Notification Modal -->
    <Teleport to="body">
      <div v-if="showNotification" class="notification">
        <div class="notification-content">
          <p>{{ notificationMessage }}</p>
          <button @click="showNotification = false" class="close-btn">Close</button>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, defineProps, defineEmits } from 'vue';
import { useRouter } from 'vue-router';
import axios from '../utils/axios';
import Pagination from './utility/Pagination.vue';

const props = defineProps<{ showAutoAssignModal: boolean }>();
const emit = defineEmits(['close-auto-assign-modal']);

const router = useRouter();
const agents = ref<any[]>([]);
const selectedCaseId = ref<number | null>(null);
const isCheckingCase = ref(false);
const showConfirmationModal = ref(false);
const confirmationMessage = ref('');
const caseIdToAssign = ref<number | null>(null);
const availableCases = ref<any[]>([]);
const showNotification = ref(false);
const notificationMessage = ref('');

const paginationState = reactive({
  currentPage: 1,
  totalPages: 0,
  totalItems: 0,
  pageSize: 10
});

const fetchAgents = async () => {
  try {
    const response = await axios.get('/api/agents', {
      params: {
        page: paginationState.currentPage,
        limit: paginationState.pageSize
      }
    });
    agents.value = response.data.data;
    paginationState.totalItems = response.data.meta.total;
    paginationState.totalPages = response.data.meta.totalPages;
    paginationState.currentPage = response.data.meta.page;
  } catch (error) {
    console.error('Error fetching agents:', error);
  }
};

const deleteAgent = async (agentId: number) => {
  const confirmed = confirm(`Are you sure you want to delete agent ${agentId}? This action cannot be undone.`);
  if (!confirmed) return;

  try {
    await axios.delete(`/api/agents/${agentId}`);
    alert('Agent deleted successfully');
    fetchAgents();
  } catch (error: any) {
    console.error('Error deleting agent:', error);
    alert('Failed to delete agent. Please try again.');
  }
};

const getCompletedCases = (agent: any) => {
  return agent.caseConnections?.filter((conn: any) => conn.status === 'COMPLETED').length || 0;
};

const viewAgentDetails = (agentId: number) => {
  router.push(`/agents/${agentId}`);
};

const checkCaseAssignment = async () => {
  if (!selectedCaseId.value) {
    alert('Please enter a case ID');
    return;
  }

  try {
    isCheckingCase.value = true;
    const response = await axios.get(`/api/agents/check-case/${selectedCaseId.value}`);
    const caseStatus = response.data;
    
    if (caseStatus?.isAssigned) {
      alert(`Case ${selectedCaseId.value} is already assigned to agent ${caseStatus.agentName}`);
      closeAutoAssignModal();
      selectedCaseId.value = null;
      return;
    }

    const responsePost = await axios.post(`/api/agents/auto-assign/${selectedCaseId.value}`);
    closeAutoAssignModal();
    selectedCaseId.value = null;

    const { agent, patientCase } = responsePost.data;
    if (!agent || !patientCase) {
      throw new Error('Failed to retrieve agent or patient case details after assignment.');
    }

    const agentName = agent.name;
    const patientName = `${patientCase.patient?.firstName || ''} ${patientCase.patient?.lastName || ''}`.trim();
    const prescriberName = `${patientCase.prescriber?.firstName || ''} ${patientCase.prescriber?.lastName || ''}`.trim();

    notificationMessage.value = `${agentName} assigned the case ${patientName} - ${prescriberName}.`;
    showNotification.value = true;

    setTimeout(() => { showNotification.value = false; }, 5000);
    await fetchAgents();
  } catch (error: any) {
    console.error('Error auto-assigning agent:', error);
    if (error.response?.status === 404) {
      alert('No suitable agent found for auto-assignment.');
    } else {
      alert('Failed to auto-assign agent. Please try again.');
    }
  } finally {
    isCheckingCase.value = false;
  }
};

const handlePageChange = (page: number) => {
  paginationState.currentPage = page;
  fetchAgents();
};

const handlePageSizeChange = (pageSize: number) => {
  paginationState.pageSize = pageSize;
  paginationState.currentPage = 1;
  fetchAgents();
};

const fetchAvailableCases = async () => {
  try {
    const response = await axios.get('/api/patient-cases');
    availableCases.value = response.data.data;
  } catch (error) {
    console.error('Error fetching available cases:', error);
    availableCases.value = [];
  }
};

const autoAssignAgent = () => { checkCaseAssignment(); };
const closeAutoAssignModal = () => { emit('close-auto-assign-modal'); };

onMounted(() => {
  fetchAgents();
  fetchAvailableCases();
});
</script>
