<template>
  <div class="bg-white shadow rounded-lg">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900">Organization Management</h3>
        <button
          @click="createNewOrganization"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
        >
          Create New Organization
        </button>
      </div>
    </div>

    <!-- Organizations Table -->
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created Date</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="organization in organizations" :key="organization.id">
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
              {{ organization.id }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div v-if="editingOrganization?.id === organization.id" class="flex items-center space-x-2">
                <input
                  v-model="editingOrganization.name"
                  type="text"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  @keyup.enter="saveOrganization"
                  @keyup.escape="cancelEdit"
                />
                <button
                  @click="saveOrganization"
                  class="text-green-600 hover:text-green-900"
                  title="Save"
                >
                  <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                </button>
                <button
                  @click="cancelEdit"
                  class="text-red-600 hover:text-red-900"
                  title="Cancel"
                >
                  <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div v-else class="text-sm text-gray-900">
                {{ organization.name }}
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ formatDate(organization.created_at) }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex space-x-2">
                <button
                  v-if="!editingOrganization || editingOrganization.id !== organization.id"
                  @click="editOrganization(organization)"
                  class="text-indigo-600 hover:text-indigo-900"
                  title="Edit Organization"
                >
                  <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </button>
                <button
                  v-if="organization.id !== 'HUB-1'"
                  @click="deleteOrganization(organization)"
                  class="text-red-600 hover:text-red-900"
                  title="Delete Organization"
                >
                  <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div class="px-6 py-4 border-t border-gray-200">
      <Pagination
        :current-page="paginationState.currentPage"
        :total-pages="paginationState.totalPages"
        :total-items="paginationState.totalItems"
        :page-size="paginationState.pageSize"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      />
    </div>

    <!-- Create Organization Modal -->
    <div v-if="showCreateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Create New Organization</h3>
          <div class="mb-4">
            <label for="orgName" class="block text-sm font-medium text-gray-700 mb-2">
              Organization Name
            </label>
            <input
              id="orgName"
              v-model="newOrganizationName"
              type="text"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="Enter organization name"
              @keyup.enter="createOrganization"
            />
          </div>
          <div class="flex justify-end space-x-3">
            <button
              @click="cancelCreate"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              @click="createOrganization"
              :disabled="!newOrganizationName.trim()"
              class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Create
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue';
import axios from 'axios';
import Pagination from './utility/Pagination.vue';

interface Organization {
  id: string;
  name: string;
  created_at: string;
}

const organizations = ref<Organization[]>([]);
const editingOrganization = ref<Organization | null>(null);
const showCreateModal = ref(false);
const newOrganizationName = ref('');

const paginationState = reactive({
  currentPage: 1,
  totalPages: 0,
  totalItems: 0,
  pageSize: 10
});

const fetchOrganizations = async () => {
  try {
    const response = await axios.get('/api/organizations', {
      params: {
        page: paginationState.currentPage,
        limit: paginationState.pageSize,
      }
    });

    organizations.value = response.data.data;
    paginationState.totalItems = response.data.meta.total;
    paginationState.totalPages = response.data.meta.totalPages;
  } catch (error) {
    console.error('Error fetching organizations:', error);
  }
};

const handlePageChange = (page: number) => {
  paginationState.currentPage = page;
  fetchOrganizations();
};

const handlePageSizeChange = (size: number) => {
  paginationState.pageSize = size;
  paginationState.currentPage = 1;
  fetchOrganizations();
};

const formatDate = (date: string): string => {
  return new Date(date).toLocaleDateString();
};

const editOrganization = (organization: Organization) => {
  editingOrganization.value = { ...organization };
};

const saveOrganization = async () => {
  if (!editingOrganization.value) return;

  try {
    await axios.put(`/api/organizations/${editingOrganization.value.id}`, {
      name: editingOrganization.value.name
    });
    
    editingOrganization.value = null;
    fetchOrganizations();
  } catch (error) {
    console.error('Error updating organization:', error);
  }
};

const cancelEdit = () => {
  editingOrganization.value = null;
};

const createNewOrganization = () => {
  showCreateModal.value = true;
  newOrganizationName.value = '';
};

const createOrganization = async () => {
  if (!newOrganizationName.value.trim()) return;

  try {
    await axios.post('/api/organizations', {
      name: newOrganizationName.value.trim()
    });
    
    showCreateModal.value = false;
    newOrganizationName.value = '';
    fetchOrganizations();
  } catch (error) {
    console.error('Error creating organization:', error);
  }
};

const cancelCreate = () => {
  showCreateModal.value = false;
  newOrganizationName.value = '';
};

const deleteOrganization = async (organization: Organization) => {
  if (organization.id === 'HUB-1') {
    alert('Cannot delete the default organization');
    return;
  }

  if (confirm(`Are you sure you want to delete "${organization.name}"?`)) {
    try {
      await axios.delete(`/api/organizations/${organization.id}`);
      fetchOrganizations();
    } catch (error) {
      console.error('Error deleting organization:', error);
    }
  }
};

onMounted(() => {
  fetchOrganizations();
});
</script>
