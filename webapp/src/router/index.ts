// src/router/index.ts
import { createRouter, createWebHistory } from "vue-router";
import CaseDetails from "../components/CaseDetails.vue";
import Dashboard from "../components/Dashboard.vue";
import OcrImport from "../components/OcrImport.vue";
import PatientDetails from "../components/PatientDetails.vue";
import PrescriberDetails from "../components/PrescriberDetails.vue";
import AgentList from '../components/AgentList.vue';
import AgentDetail from '../components/AgentDetail.vue';
import AgentCreate from '../views/AgentCreate.vue';
import Login from '../views/Login.vue';
import Register from '../views/Register.vue';
import { useOrganization } from '../contexts/OrganizationContext';

const routes = [
  {
    path: "/login",
    name: "login",
    component: Login,
    meta: { requiresAuth: false }
  },
  {
    path: "/register",
    name: "register",
    component: Register,
    meta: { requiresAuth: false }
  },
  {
    path: "/case/new",
    name: "NewCase",
    component: CaseDetails,
    meta: { requiresAuth: true }
  },
  {
    path: "/",
    redirect: "/dashboard"
  },
  {
    path: "/case/:caseId",
    name: "case-details",
    component: CaseDetails,
    props: true,
    meta: { requiresAuth: true }
  },
  {
    path: "/patient/new",
    name: "NewPatient",
    component: PatientDetails,
    meta: { requiresAuth: true }
  },
  {
    path: "/patient/:patientId",
    name: "patient-details",
    component: PatientDetails,
    props: true,
    meta: { requiresAuth: true }
  },
  {
    path: "/prescriber/new",
    name: "NewPrescriber",
    component: PrescriberDetails,
    meta: { requiresAuth: true }
  },
  {
    path: "/prescriber/:prescriberId",
    name: "prescriber-details",
    component: PrescriberDetails,
    props: true,
    meta: { requiresAuth: true }
  },
  {
    path: "/dashboard",
    name: "dashboard",
    component: Dashboard,
    meta: { requiresAuth: true }
  },
  {
    path: "/ocr-import",
    name: "ocr-import",
    component: OcrImport,
    meta: { requiresAuth: true }
  },
  {
    path: '/agents',
    name: 'agents',
    component: AgentList,
    meta: { requiresAuth: true }
  },
  {
    path: '/agents/:id',
    name: 'agent-details',
    component: AgentDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/agents/create',
    name: 'AgentCreate',
    component: AgentCreate,
    meta: { requiresAuth: true }
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// Navigation guard with organization initialization
router.beforeEach(async (to, from, next) => {
  const token = localStorage.getItem('token');
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);

  if (requiresAuth && !token) {
    // Clear any existing organization data when not authenticated
    const { clearOrganization } = useOrganization();
    clearOrganization();
    next('/login');
  } else if ((to.path === '/login' || to.path === '/register') && token) {
    next('/');
  } else if (requiresAuth && token) {
    // Initialize organization context for authenticated routes
    try {
      const { initializeOrganization } = useOrganization();
      await initializeOrganization();
      next();
    } catch (error) {
      console.error('Failed to initialize organization:', error);
      // If organization initialization fails, still proceed but log the error
      next();
    }
  } else {
    next();
  }
});

export default router;
