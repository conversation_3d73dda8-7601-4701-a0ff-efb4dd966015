const bcrypt = require('bcrypt');

async function generateCorrectHash() {
    const password = 'admin123';
    const saltRounds = 10;
    
    console.log('Generating hash for password:', password);
    
    try {
        const hash = await bcrypt.hash(password, saltRounds);
        console.log('Generated hash:', hash);
        
        // Verify the hash works
        const isValid = await bcrypt.compare(password, hash);
        console.log('Hash verification:', isValid);
        
        if (isValid) {
            console.log('\n✅ Hash is valid! Use this hash in the database:');
            console.log(hash);
            
            console.log('\n📝 SQL command to update admin user:');
            console.log(`UPDATE agent SET password = '${hash}' WHERE email = '<EMAIL>';`);
        }
        
    } catch (error) {
        console.error('Error generating hash:', error);
    }
}

generateCorrectHash();
