const bcrypt = require('bcrypt');

async function testPassword() {
    const password = 'admin123';
    const storedHash = '$2b$10$CwTycUXWue0Thq9StjUM0uBUcyJ6unflVjZRWwDs2qODeFlLGAul2';
    
    console.log('Testing password:', password);
    console.log('Against hash:', storedHash);
    
    try {
        const isValid = await bcrypt.compare(password, storedHash);
        console.log('Password match result:', isValid);
        
        if (!isValid) {
            console.log('❌ Password does not match hash');
            console.log('Creating new hash...');
            const newHash = await bcrypt.hash(password, 10);
            console.log('New hash:', newHash);
            
            // Test the new hash
            const newIsValid = await bcrypt.compare(password, newHash);
            console.log('New hash validation:', newIsValid);
        } else {
            console.log('✅ Password matches hash');
        }
    } catch (error) {
        console.error('Error testing password:', error);
    }
}

testPassword();
