const bcrypt = require('bcrypt');

async function createCustomPassword() {
    // Change this to whatever password you want
    const password = 'password123';  // or 'admin', 'test123', etc.
    const saltRounds = 10;
    
    console.log('Creating hash for password:', password);
    
    try {
        const hash = await bcrypt.hash(password, saltRounds);
        console.log('Generated hash:', hash);
        
        // Verify the hash works
        const isValid = await bcrypt.compare(password, hash);
        console.log('Hash verification:', isValid);
        
        if (isValid) {
            console.log('\n✅ Hash is valid!');
            console.log('Password:', password);
            console.log('Hash:', hash);
            
            console.log('\n📝 SQL command:');
            console.log(`UPDATE agent SET password = '${hash}' WHERE email = '<EMAIL>';`);
        }
        
    } catch (error) {
        console.error('Error:', error);
    }
}

createCustomPassword();
