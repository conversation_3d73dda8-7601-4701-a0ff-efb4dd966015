import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddRoleAndAddressToAgent1703000000000 implements MigrationInterface {
    name = 'AddRoleAndAddressToAgent1703000000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create enum type for role
        await queryRunner.query(`CREATE TYPE "agent_role_enum" AS ENUM('admin', 'agent')`);
        
        // Add role column to agent table
        await queryRunner.addColumn('agent', new TableColumn({
            name: 'role',
            type: 'enum',
            enum: ['admin', 'agent'],
            enumName: 'agent_role_enum',
            default: "'agent'",
            isNullable: false,
        }));

        // Add address column to agent table
        await queryRunner.addColumn('agent', new TableColumn({
            name: 'address',
            type: 'varchar',
            length: '500',
            isNullable: true,
        }));

        // Update existing agents to have agent role by default
        await queryRunner.query(`UPDATE agent SET role = 'agent' WHERE role IS NULL`);
        
        console.log('✅ Added role and address columns to agent table');
        console.log('✅ Set default role as "agent" for existing agents');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove columns
        await queryRunner.dropColumn('agent', 'address');
        await queryRunner.dropColumn('agent', 'role');
        
        // Drop enum type
        await queryRunner.query(`DROP TYPE "agent_role_enum"`);
        
        console.log('✅ Removed role and address columns from agent table');
    }
}
