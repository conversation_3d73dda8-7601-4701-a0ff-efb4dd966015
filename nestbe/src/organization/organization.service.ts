import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Organization } from './entities/organization.entity';
import { CreateOrganizationDto } from './dto/create-organization.dto';
import { UpdateOrganizationDto } from './dto/update-organization.dto';

interface PaginatedOrganizationResponse {
  data: Organization[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

@Injectable()
export class OrganizationService {
    private static instance: OrganizationService;

    constructor(
        @InjectRepository(Organization)
        private readonly organizationRepository: Repository<Organization>,
    ) {
        OrganizationService.instance = this;
    }

    static getInstance(): OrganizationService {
        return OrganizationService.instance;
    }

    async findAll(): Promise<{ data: Organization[] }> {
        const organizations = await this.organizationRepository.find();
        return { data: organizations };
    }

    async findAllPaginated(page = 1, limit = 10): Promise<PaginatedOrganizationResponse> {
        const skip = (page - 1) * limit;
        
        const [organizations, total] = await this.organizationRepository.findAndCount({
            skip,
            take: limit,
            order: { created_at: 'DESC' },
        });

        const totalPages = Math.ceil(total / limit);

        return {
            data: organizations,
            meta: {
                total,
                page,
                limit,
                totalPages,
            },
        };
    }

    async findOne(id: string): Promise<Organization> {
        const organization = await this.organizationRepository.findOne({ where: { id } });
        if (!organization) {
            throw new NotFoundException(`Organization with ID ${id} not found`);
        }
        return organization;
    }

    async create(createOrganizationDto: CreateOrganizationDto): Promise<Organization> {
        const organization = this.organizationRepository.create(createOrganizationDto);
        await this.generateId(organization);
        return this.organizationRepository.save(organization);
    }

    async update(id: string, updateOrganizationDto: UpdateOrganizationDto): Promise<Organization> {
        const organization = await this.findOne(id);
        Object.assign(organization, updateOrganizationDto);
        return this.organizationRepository.save(organization);
    }

    async remove(id: string): Promise<void> {
        const organization = await this.findOne(id);
        await this.organizationRepository.remove(organization);
    }

    async getDefaultOrganization(): Promise<Organization> {
        let defaultOrg = await this.organizationRepository.findOne({ 
            where: { id: 'HUB-1' } 
        });
        
        if (!defaultOrg) {
            defaultOrg = this.organizationRepository.create({
                id: 'HUB-1',
                name: 'Default Organization',
            });
            defaultOrg = await this.organizationRepository.save(defaultOrg);
        }
        
        return defaultOrg;
    }

    private async generateId(organization: Organization): Promise<void> {
        const latestOrg = await this.organizationRepository.findOne({
            where: {},
            order: {
                id: 'DESC'
            }
        });
        const nextNumber = latestOrg ? parseInt(latestOrg.id.split('-')[1]) + 1 : 1;
        organization.id = `HUB-${nextNumber}`;
    }

    static async getLatestOrganization(): Promise<Organization | null> {
        const instance = OrganizationService.getInstance();
        if (!instance) return null;

        return instance.organizationRepository.findOne({
            where: {},
            order: {
                id: 'DESC'
            }
        });
    }
}
