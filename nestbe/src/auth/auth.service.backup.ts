import { Injectable, UnauthorizedException, ConflictException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Agent } from '../agent/agent.entity';
import { RegisterDto } from './dto/register.dto';
import * as bcrypt from 'bcrypt';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(Agent)
    private agentRepository: Repository<Agent>,
    private jwtService: JwtService,
  ) {}

  async validateUser(email: string, password: string): Promise<any> {
    const user = await this.agentRepository.findOne({ where: { email } });
    if (user && await bcrypt.compare(password, user.password)) {
      const { password, ...result } = user;
      return result;
    }
    return null;
  }

  async register(registerDto: RegisterDto) {
    const existingUser = await this.agentRepository.findOne({
      where: { email: registerDto.email }
    });

    if (existingUser) {
      throw new ConflictException('Email already exists');
    }

    const hashedPassword = await bcrypt.hash(registerDto.password, 10);

    const user = this.agentRepository.create({
      ...registerDto,
      password: hashedPassword,
    });

    await this.agentRepository.save(user);

    const { password, ...result } = user;
    return result;
  }

  async login(email: string, password: string, organizationId: string) {
    const user = await this.validateUser(email, password);
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // If organizationId is provided, validate that the user belongs to that organization
    if (organizationId && user.organization_id !== organizationId) {
      throw new UnauthorizedException('User does not belong to the specified organization');
    }

    const payload = { 
      email: user.email, 
      sub: user.id,
      organization_id: user.organization_id 
    };

    return {
      access_token: this.jwtService.sign(payload),
      user: {
        id: user.id,
        email: user.email,
        organization_id: user.organization_id
      }
    };
  }
} 