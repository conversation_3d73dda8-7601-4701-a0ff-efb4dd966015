import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AgentService } from './agent.service';
import { CreateAgentDto } from './dto/create-agent.dto';
import { UpdateAgentDto } from './dto/update-agent.dto';
import { Agent, UserRole } from './agent.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

// Define the interface locally to avoid export issues
interface PaginatedAgentResponse {
  data: Agent[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

@ApiTags('agents')
@Controller('agents')
@UseGuards(JwtAuthGuard)
export class AgentController {
  constructor(private readonly agentService: AgentService) {}

  @Get()
  @ApiOperation({ summary: 'Get all agents with pagination' })
  @ApiResponse({ status: 200, description: 'Return paginated agents' })
  async getAllAgents(
    @Request() req: any,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ): Promise<PaginatedAgentResponse> {
    const currentUser = req.user as Agent;
    return this.agentService.getAllAgents(page, limit, currentUser);
  }

  @Get('search')
  @ApiOperation({ summary: 'Search agents' })
  @ApiResponse({ status: 200, description: 'Return matching agents' })
  async searchAgents(
    @Request() req: any,
    @Query('name') name?: string,
    @Query('organization') organization?: string,
    @Query('skills') skills?: string,
    @Query('role') role?: UserRole,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ): Promise<PaginatedAgentResponse> {
    const currentUser = req.user as Agent;
    return this.agentService.searchAgents(
      { name, organization, skills, role, page, limit },
      currentUser,
    );
  }

  @Get('check-case/:caseId')
  @ApiOperation({ summary: 'Check if a case is assigned to an agent' })
  @ApiResponse({ status: 200, description: 'Return case assignment status' })
  async checkCaseAssignment(@Param('caseId') caseId: number) {
    return this.agentService.checkCaseAssignment(caseId);
  }

  @Post('auto-assign/:caseId')
  @ApiOperation({ summary: 'Auto-assign an agent to a case' })
  @ApiResponse({ status: 201, description: 'Agent assigned successfully' })
  async autoAssignAgent(@Param('caseId') caseId: number) {
    // This would need additional logic for auto-assignment
    // For now, we'll return a placeholder response
    throw new Error('Auto-assignment logic not implemented yet');
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get agent details by ID' })
  @ApiResponse({ status: 200, description: 'Return agent details' })
  async getAgentDetails(
    @Param('id') id: number,
    @Request() req: any,
  ): Promise<Agent> {
    const currentUser = req.user as Agent;
    return this.agentService.getAgentDetails(id, currentUser);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new agent' })
  @ApiResponse({ status: 201, description: 'Agent created successfully' })
  async createAgent(
    @Body() createAgentDto: CreateAgentDto,
    @Request() req: any,
  ): Promise<Agent> {
    const currentUser = req.user as Agent;
    return this.agentService.createAgent(createAgentDto, currentUser);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update an agent' })
  @ApiResponse({ status: 200, description: 'Agent updated successfully' })
  async updateAgent(
    @Param('id') id: number,
    @Body() updateAgentDto: UpdateAgentDto,
    @Request() req: any,
  ): Promise<Agent> {
    const currentUser = req.user as Agent;
    return this.agentService.updateAgent(id, updateAgentDto, currentUser);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete an agent' })
  @ApiResponse({ status: 200, description: 'Agent deleted successfully' })
  async deleteAgent(
    @Param('id') id: number,
    @Request() req: any,
  ): Promise<{ message: string }> {
    const currentUser = req.user as Agent;
    await this.agentService.deleteAgent(id, currentUser);
    return { message: 'Agent deleted successfully' };
  }

  @Post(':agentId/assign-case/:caseId')
  @ApiOperation({ summary: 'Assign an agent to a case' })
  @ApiResponse({ status: 201, description: 'Agent assigned to case successfully' })
  async assignAgentToCase(
    @Param('agentId') agentId: number,
    @Param('caseId') caseId: number,
  ) {
    return this.agentService.assignAgentToCase(agentId, caseId);
  }
}
