import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedC<PERSON>umn, CreateDateColumn, UpdateDateColumn, OneToMany, ManyToOne, JoinColumn } from 'typeorm';
import { AgentCaseConnection } from './agent-case-connection.entity';
import { Organization } from '../organization/entities/organization.entity';

export enum UserRole {
  ADMIN = 'admin',
  AGENT = 'agent'
}

@Entity()
export class Agent {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  email: string;

  @Column()
  password: string;

  @Column()
  firstName: string;

  @Column()
  lastName: string;

  @Column()
  organization_id: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.AGENT
  })
  role: UserRole;

  @Column({ type: 'float', default: 0 })
  aiClassifiedScore: number;

  @Column({ type: 'json', nullable: true })
  completedSkills: string[];

  @Column({ type: 'int', default: 0 })
  completedDuration: number;

  @Column({ nullable: true })
  address: string;

  @ManyToOne(() => Organization)
  @JoinColumn({ name: 'organization_id' })
  organization: Organization;

  @OneToMany(() => AgentCaseConnection, connection => connection.agent)
  caseConnections: AgentCaseConnection[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Virtual properties for display
  get name(): string {
    return `${this.firstName} ${this.lastName}`;
  }

  get completedCases(): number {
    return this.caseConnections?.filter(conn => conn.status === 'completed').length || 0;
  }
}
